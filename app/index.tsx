import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect, useState } from 'react';
import { Al<PERSON>, ScrollView, StyleSheet, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { LoadingSpinner } from '../src/components/common';
import {
  ActionButtons,
  PredictedPassRateComponent,
  QuickStats,
  VolumeProgressCard,
} from '../src/components/dashboard';
import { useDatabase } from '../src/hooks/useDatabase';
import { useAppStore, usePreferencesStore } from '../src/store';
import { OverallStats, PredictedPassRate, VolumeStats } from '../src/types/statistics';
import { COLORS } from '../src/utils/constants';

export default function DashboardScreen() {
  const { isInitializing, setInitializing } = useAppStore();
  const { isInitialized, error } = useDatabase();
  const { loadPreferences, isLoaded } = usePreferencesStore();
  const [loading, setLoading] = useState(true);

  // Sample data for MVP - based on real question counts
  const [mockVolumeStats] = useState<VolumeStats[]>([
    { volume: 1, totalQuestions: 176, seenQuestions: 85, correctAnswers: 68, wrongAnswers: 17, accuracy: 80.0, lastPracticeDate: new Date() },
    { volume: 2, totalQuestions: 66, seenQuestions: 35, correctAnswers: 28, wrongAnswers: 7, accuracy: 80.0 },
    { volume: 3, totalQuestions: 150, seenQuestions: 75, correctAnswers: 56, wrongAnswers: 19, accuracy: 74.7 },
    { volume: 4, totalQuestions: 139, seenQuestions: 45, correctAnswers: 32, wrongAnswers: 13, accuracy: 71.1 },
    { volume: 5, totalQuestions: 116, seenQuestions: 25, correctAnswers: 18, wrongAnswers: 7, accuracy: 72.0 },
  ]);

  const [mockOverallStats] = useState<OverallStats>({
    totalQuestionsAnswered: 265,
    correctAnswers: 202,
    wrongAnswers: 63,
    accuracy: 76.2,
    streakCount: 12,
    totalTimeSpent: 11900, // 3.3 hours
    averageTimePerQuestion: 45,
    volumeStats: mockVolumeStats,
  });

  const [mockPrediction] = useState<PredictedPassRate>({
    percentage: 76,
    confidence: 'medium',
    recommendations: [
      '加強第2冊和第3冊的練習',
      '建議完成至少200道題目後再參加考試',
      '重點複習錯題集',
    ],
  });

  useEffect(() => {
    const initialize = async () => {
      // Load preferences on app start
      if (!isLoaded) {
        await loadPreferences();
      }
      
      if (isInitialized && !error) {
        setInitializing(false);
        setLoading(false);
      } else if (error) {
        setLoading(false);
        Alert.alert('數據庫錯誤', error);
      }
    };
    
    initialize();
  }, [isInitialized, error, setInitializing, isLoaded, loadPreferences]);

  const handleStartPractice = () => {
    router.push('/practice');
  };

  const handleStartExam = () => {
    Alert.alert('模擬考試', '考試功能正在開發中，敬請期待！');
  };

  const handleViewHistory = () => {
    Alert.alert('練習記錄', '歷史記錄功能正在開發中，敬請期待！');
  };

  const handleViewWrongQuestions = () => {
    Alert.alert('錯題集', '錯題集功能正在開發中，敬請期待！');
  };

  const handleVolumePress = (volume: number) => {
    router.push(`/practice?volume=${volume}`);
  };

  if (isInitializing || loading) {
    return (
      <SafeAreaView style={styles.container}>
        <LoadingSpinner message="正在初始化..." />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      
      <ScrollView 
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.section}>
          <Text style={styles.welcomeText}>歡迎回來！</Text>
          <Text style={styles.subtitleText}>繼續您的駕考學習之旅</Text>
        </View>

        <View style={styles.section}>
          <ActionButtons
            onStartPractice={handleStartPractice}
            onStartExam={handleStartExam}
            onViewHistory={handleViewHistory}
            onViewWrongQuestions={handleViewWrongQuestions}
          />
        </View>

        <View style={styles.section}>
          <QuickStats stats={mockOverallStats} />
        </View>

        <View style={styles.section}>
          <PredictedPassRateComponent prediction={mockPrediction} />
        </View>

        <View style={styles.volumesSection}>
          <Text style={styles.sectionTitle}>各冊進度</Text>
          <View style={styles.volumeGrid}>
            {mockVolumeStats.map((stats) => (
              <View key={stats.volume} style={styles.volumeCardContainer}>
                <VolumeProgressCard
                  volumeStats={stats}
                  onPress={() => handleVolumePress(stats.volume)}
                />
              </View>
            ))}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollContainer: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    gap: 16,
  },
  section: {
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT,
    marginBottom: 2,
  },
  subtitleText: {
    fontSize: 14,
    color: COLORS.SECONDARY_TEXT,
  },
  volumesSection: {
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.TEXT,
    marginBottom: 12,
  },
  volumeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  volumeCardContainer: {
    width: '48%',
    marginBottom: 16,
  },
});