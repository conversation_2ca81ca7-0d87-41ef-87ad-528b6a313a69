import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import { Al<PERSON>, ScrollView, StyleSheet, Text, View } from 'react-native';

import { <PERSON><PERSON>, <PERSON>, Header, LoadingSpinner } from '../../src/components/common';
import { QuestionManager } from '../../src/services/questions/manager';
import { usePracticeStore } from '../../src/store';
import { PracticeConfig } from '../../src/types/session';
import { COLORS, PRACTICE_CONFIG, VOLUMES } from '../../src/utils/constants';

export default function PracticeOptionsScreen() {
  const [loading, setLoading] = useState(false);
  const { startSession } = usePracticeStore();

  const startPractice = async (config: PracticeConfig) => {
    setLoading(true);
    
    try {
      const questions = await QuestionManager.generatePracticeQuestions(config);
      
      if (questions.length === 0) {
        Alert.alert('沒有題目', '所選範圍內沒有找到題目，請選擇其他選項。');
        return;
      }

      startSession(config, questions);
      router.push('/practice/session');
    } catch (error) {
      console.error('Failed to start practice:', error);
      Alert.alert('錯誤', '無法載入練習題目，請稍後再試。');
    } finally {
      setLoading(false);
    }
  };

  const handleVolumeSelect = (volume: number) => {
    const config: PracticeConfig = {
      volume,
      chapter: null,
      questionCount: PRACTICE_CONFIG.DEFAULT_QUESTION_COUNT,
      mode: 'sequential',
      includeWrongQuestions: false,
      includeBookmarked: false,
    };
    
    startPractice(config);
  };

  const handleAllVolumes = () => {
    const config: PracticeConfig = {
      volume: null,
      chapter: null,
      questionCount: PRACTICE_CONFIG.DEFAULT_QUESTION_COUNT,
      mode: 'random',
      includeWrongQuestions: false,
      includeBookmarked: false,
    };
    
    startPractice(config);
  };

  const handleQuickStart = () => {
    const config: PracticeConfig = {
      volume: 1, // Start with volume 1
      chapter: null,
      questionCount: 10, // Quick practice with 10 questions
      mode: 'sequential',
      includeWrongQuestions: false,
      includeBookmarked: false,
    };
    
    startPractice(config);
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <LoadingSpinner message="正在載入題目..." />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header 
        title="選擇練習模式"
        leftAction={{
          icon: '‹',
          onPress: () => router.back(),
        }}
      />
      <StatusBar style="auto" />
      <ScrollView contentContainerStyle={styles.scrollContent}>

        <Card style={styles.quickStartCard}>
          <Text style={styles.cardTitle}>🚀 快速開始</Text>
          <Text style={styles.cardDescription}>
            第一冊 10 道題目，適合快速練習
          </Text>
          <Button
            title="立即開始"
            onPress={handleQuickStart}
            style={styles.cardButton}
            textStyle={styles.cardButtonText}
          />
        </Card>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>分冊練習</Text>
          {Array.from({ length: VOLUMES.TOTAL }, (_, i) => i + 1).map(volume => (
            <Card
              key={volume}
              style={styles.volumeCard}
              onPress={() => handleVolumeSelect(volume)}
            >
              <Text style={styles.volumeTitle}>
                第 {volume} 冊
              </Text>
              <Text style={styles.volumeSubtitle}>
                {VOLUMES.NAMES[volume as keyof typeof VOLUMES.NAMES]}
              </Text>
              <Text style={styles.volumeDescription}>
                共 {VOLUMES.COUNTS[volume as keyof typeof VOLUMES.COUNTS]} 道題目 • 練習 {PRACTICE_CONFIG.DEFAULT_QUESTION_COUNT} 題
              </Text>
            </Card>
          ))}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>綜合練習</Text>
          
          <Card style={styles.allVolumesCard}>
            <Text style={styles.cardTitle}>📚 全冊混合</Text>
            <Text style={styles.cardDescription}>
              從所有冊別隨機抽取 {PRACTICE_CONFIG.DEFAULT_QUESTION_COUNT} 道題目
            </Text>
            <Button
              title="開始綜合練習"
              onPress={handleAllVolumes}
              variant="secondary"
              style={styles.cardButton}
            />
          </Card>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollContent: {
    padding: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 24,
    color: COLORS.TEXT,
  },
  quickStartCard: {
    marginBottom: 32,
    backgroundColor: COLORS.PRIMARY,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    color: '#ffffff',
  },
  cardDescription: {
    fontSize: 16,
    marginBottom: 16,
    color: '#ffffff',
    opacity: 0.9,
  },
  cardButton: {
    backgroundColor: '#ffffff',
  },
  cardButtonText: {
    color: COLORS.PRIMARY,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
    color: COLORS.TEXT,
  },
  volumeCard: {
    marginBottom: 12,
  },
  volumeTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: COLORS.PRIMARY,
    marginBottom: 4,
  },
  volumeSubtitle: {
    fontSize: 16,
    color: COLORS.TEXT,
    marginBottom: 8,
  },
  volumeDescription: {
    fontSize: 14,
    color: COLORS.SECONDARY_TEXT,
  },
  allVolumesCard: {
    backgroundColor: COLORS.SUCCESS,
  },
});