import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useState } from 'react';
import { Alert, ScrollView, StyleSheet, Text, View } from 'react-native';

import { <PERSON><PERSON>, <PERSON>, Header, LoadingSpinner } from '../../src/components/common';
import { QuestionManager } from '../../src/services/questions/manager';
import { usePracticeStore } from '../../src/store';
import { PracticeConfig } from '../../src/types/session';
import { COLORS, VOLUMES } from '../../src/utils/constants';

export default function PracticeOptionsScreen() {
  const [loading, setLoading] = useState(false);
  const [selectedVolumes, setSelectedVolumes] = useState<number[]>([]);
  const [includeUnseen, setIncludeUnseen] = useState(true);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [practiceMode, setPracticeMode] = useState<'sequential' | 'random'>('sequential');
  const { startSession } = usePracticeStore();

  const startPractice = async (config: PracticeConfig) => {
    setLoading(true);
    
    try {
      const questions = await QuestionManager.generatePracticeQuestions(config);
      
      if (questions.length === 0) {
        Alert.alert('沒有題目', '所選範圍內沒有找到題目，請選擇其他選項。');
        return;
      }

      startSession(config, questions);
      router.push('/practice/session');
    } catch (error) {
      console.error('Failed to start practice:', error);
      Alert.alert('錯誤', '無法載入練習題目，請稍後再試。');
    } finally {
      setLoading(false);
    }
  };

  const toggleVolumeSelection = (volume: number) => {
    setSelectedVolumes(prev => {
      if (prev.includes(volume)) {
        return prev.filter(v => v !== volume);
      } else {
        return [...prev, volume].sort();
      }
    });
  };

  const handleStartPractice = () => {
    if (selectedVolumes.length === 0) {
      Alert.alert('請選擇冊別', '請至少選擇一個冊別進行練習。');
      return;
    }

    const config: PracticeConfig = {
      volumes: selectedVolumes,
      chapter: null,
      mode: practiceMode,
      includeWrongQuestions: false,
      includeBookmarked: false,
      includeUnseen,
    };

    startPractice(config);
  };

  const handleQuickStart = () => {
    const config: PracticeConfig = {
      volumes: [1], // Start with volume 1
      chapter: null,
      mode: 'sequential',
      includeWrongQuestions: false,
      includeBookmarked: false,
      includeUnseen: true,
    };

    startPractice(config);
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <LoadingSpinner message="正在載入題目..." />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Header 
        title="選擇練習模式"
        leftAction={{
          icon: '‹',
          onPress: () => router.back(),
        }}
      />
      <StatusBar style="auto" />
      <ScrollView contentContainerStyle={styles.scrollContent}>

        <Card style={styles.quickStartCard}>
          <Text style={styles.cardTitle}>🚀 快速開始</Text>
          <Text style={styles.cardDescription}>
            第一冊練習，適合快速開始
          </Text>
          <Button
            title="立即開始"
            onPress={handleQuickStart}
            style={styles.cardButton}
            textStyle={styles.cardButtonText}
          />
        </Card>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>選擇練習冊別</Text>
          <Text style={styles.sectionSubtitle}>可選擇一個或多個冊別進行練習</Text>

          <View style={styles.volumeGrid}>
            {Array.from({ length: VOLUMES.TOTAL }, (_, i) => i + 1).map(volume => {
              const isSelected = selectedVolumes.includes(volume);
              return (
                <Card
                  key={volume}
                  style={[
                    styles.volumeCard,
                    isSelected && styles.selectedVolumeCard
                  ]}
                  onPress={() => toggleVolumeSelection(volume)}
                >
                  <View style={styles.volumeHeader}>
                    <Text style={[
                      styles.volumeTitle,
                      isSelected && styles.selectedVolumeTitle
                    ]}>
                      第 {volume} 冊
                    </Text>
                    <View style={[
                      styles.checkbox,
                      isSelected && styles.checkedBox
                    ]}>
                      {isSelected && <Text style={styles.checkmark}>✓</Text>}
                    </View>
                  </View>
                  <Text style={[
                    styles.volumeSubtitle,
                    isSelected && styles.selectedVolumeSubtitle
                  ]}>
                    {VOLUMES.NAMES[volume as keyof typeof VOLUMES.NAMES]}
                  </Text>
                  <Text style={[
                    styles.volumeDescription,
                    isSelected && styles.selectedVolumeDescription
                  ]}>
                    共 {VOLUMES.COUNTS[volume as keyof typeof VOLUMES.COUNTS]} 道題目
                  </Text>
                </Card>
              );
            })}
          </View>
        </View>

        {/* Practice Options */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>練習選項</Text>

          <Card style={styles.optionCard}>
            <View style={styles.optionRow}>
              <Text style={styles.optionLabel}>題目範圍</Text>
              <View style={styles.optionButtons}>
                <Button
                  title="只練習未做過的"
                  onPress={() => setIncludeUnseen(true)}
                  variant={includeUnseen ? 'primary' : 'secondary'}
                  size="small"
                  style={styles.optionButton}
                />
                <Button
                  title="全部題目"
                  onPress={() => setIncludeUnseen(false)}
                  variant={!includeUnseen ? 'primary' : 'secondary'}
                  size="small"
                  style={styles.optionButton}
                />
              </View>
            </View>
          </Card>

          {/* Advanced Settings */}
          <Card style={styles.optionCard}>
            <View style={styles.optionRow}>
              <Text style={styles.optionLabel}>進階設定</Text>
              <Button
                title={showAdvanced ? "收起 ▲" : "展開 ▼"}
                onPress={() => setShowAdvanced(!showAdvanced)}
                variant="secondary"
                size="small"
              />
            </View>

            {showAdvanced && (
              <View style={styles.advancedOptions}>
                <View style={styles.optionRow}>
                  <Text style={styles.optionSubLabel}>出題順序</Text>
                  <View style={styles.optionButtons}>
                    <Button
                      title="按順序"
                      onPress={() => setPracticeMode('sequential')}
                      variant={practiceMode === 'sequential' ? 'primary' : 'secondary'}
                      size="small"
                      style={styles.optionButton}
                    />
                    <Button
                      title="隨機"
                      onPress={() => setPracticeMode('random')}
                      variant={practiceMode === 'random' ? 'primary' : 'secondary'}
                      size="small"
                      style={styles.optionButton}
                    />
                  </View>
                </View>
              </View>
            )}
          </Card>
        </View>

        {/* Start Practice Button */}
        <View style={styles.section}>
          <Button
            title={`開始練習 ${selectedVolumes.length > 0 ? `(已選 ${selectedVolumes.length} 冊)` : ''}`}
            onPress={handleStartPractice}
            disabled={selectedVolumes.length === 0}
            style={[
              styles.startButton,
              selectedVolumes.length === 0 && styles.disabledButton
            ]}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollContent: {
    padding: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 24,
    color: COLORS.TEXT,
  },
  quickStartCard: {
    marginBottom: 32,
    backgroundColor: COLORS.PRIMARY,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    color: '#ffffff',
  },
  cardDescription: {
    fontSize: 16,
    marginBottom: 16,
    color: '#ffffff',
    opacity: 0.9,
  },
  cardButton: {
    backgroundColor: '#ffffff',
  },
  cardButtonText: {
    color: COLORS.PRIMARY,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 8,
    color: COLORS.TEXT,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: COLORS.SECONDARY_TEXT,
    marginBottom: 16,
  },
  volumeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  volumeCard: {
    width: '48%',
    marginBottom: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedVolumeCard: {
    borderColor: COLORS.PRIMARY,
    backgroundColor: COLORS.PRIMARY + '10',
  },
  volumeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  volumeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT,
  },
  selectedVolumeTitle: {
    color: COLORS.PRIMARY,
  },
  volumeSubtitle: {
    fontSize: 14,
    color: COLORS.TEXT,
    marginBottom: 8,
  },
  selectedVolumeSubtitle: {
    color: COLORS.PRIMARY,
  },
  volumeDescription: {
    fontSize: 12,
    color: COLORS.SECONDARY_TEXT,
  },
  selectedVolumeDescription: {
    color: COLORS.PRIMARY,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: COLORS.SECONDARY_TEXT,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkedBox: {
    backgroundColor: COLORS.PRIMARY,
    borderColor: COLORS.PRIMARY,
  },
  checkmark: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  optionCard: {
    marginBottom: 12,
  },
  optionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  optionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: COLORS.TEXT,
  },
  optionSubLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.TEXT,
  },
  optionButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  optionButton: {
    paddingHorizontal: 12,
  },
  advancedOptions: {
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  startButton: {
    marginTop: 8,
  },
  disabledButton: {
    opacity: 0.5,
  },
});