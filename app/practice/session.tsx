import React, { useEffect } from 'react';
import { View, ScrollView, StyleSheet, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';

import { usePracticeStore } from '../../src/store';
import { useRealtimeAnswer } from '../../src/hooks/useRealtimeAnswer';
import {
  QuestionCard,
  ProgressBar,
  OptionsList,
  AnswerFeedback,
} from '../../src/components/question';
import { Button } from '../../src/components/common';
import { COLORS } from '../../src/utils/constants';

export default function PracticeSessionScreen() {
  const {
    session,
    currentQuestion,
    selectedAnswer,
    showAnswer,
    isAnswerCorrect,
    stats,
    isActive,
    selectAnswer,
    submitAnswer,
    nextQuestion,
    previousQuestion,
    endSession,
  } = usePracticeStore();

  const { recordAnswer } = useRealtimeAnswer();

  useEffect(() => {
    if (!session || !isActive) {
      router.replace('/practice');
    }
  }, [session, isActive]);

  if (!session || !currentQuestion) {
    return null;
  }

  const handleAnswerSelect = (answerIndex: number) => {
    if (showAnswer) return;
    selectAnswer(answerIndex);
  };

  const handleSubmitAnswer = async () => {
    if (selectedAnswer === null || showAnswer) return;

    submitAnswer();

    // Record answer in database
    try {
      const correctOptionIndex = currentQuestion.options.findIndex(opt => opt.isCorrect);
      const selectedOptionLabel = ['A', 'B', 'C', 'D'][selectedAnswer];
      const correctOptionLabel = ['A', 'B', 'C', 'D'][correctOptionIndex];
      
      await recordAnswer({
        questionId: currentQuestion.id,
        volume: currentQuestion.volume,
        chapter: 1, // Default chapter
        isCorrect: currentQuestion.options[selectedAnswer]?.isCorrect || false,
        mode: 'practice',
        sessionId: session.id,
        selectedOption: selectedOptionLabel as 'A' | 'B' | 'C' | 'D',
        correctOption: correctOptionLabel as 'A' | 'B' | 'C' | 'D',
        timeSpent: 0, // TODO: Calculate actual time spent
      });
    } catch (error) {
      console.error('Failed to record answer:', error);
    }
  };

  const handleNext = () => {
    if (session.currentIndex < session.questions.length - 1) {
      nextQuestion();
    } else {
      // Last question - show completion dialog
      Alert.alert(
        '練習完成',
        `您已完成所有題目！\n\n正確率：${stats.accuracy.toFixed(1)}%\n答對：${stats.correctCount} 題\n答錯：${stats.wrongCount} 題`,
        [
          {
            text: '繼續練習',
            onPress: () => router.replace('/practice'),
          },
          {
            text: '返回主頁',
            onPress: () => {
              endSession();
              router.replace('/');
            },
          },
        ]
      );
    }
  };

  const handlePrevious = () => {
    if (session.currentIndex > 0) {
      previousQuestion();
    }
  };

  const handleEndSession = () => {
    Alert.alert(
      '結束練習',
      '確定要結束當前練習嗎？進度將會保存。',
      [
        { text: '取消', style: 'cancel' },
        {
          text: '確定',
          onPress: () => {
            endSession();
            router.replace('/');
          },
        },
      ]
    );
  };

  const isLastQuestion = session.currentIndex === session.questions.length - 1;
  const canGoPrevious = session.currentIndex > 0;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      
      <View style={styles.progressContainer}>
        <ProgressBar
          current={session.currentIndex + 1}
          total={session.questions.length}
          color={COLORS.PRIMARY}
        />
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <QuestionCard
          question={currentQuestion}
          questionNumber={session.currentIndex + 1}
          totalQuestions={session.questions.length}
        />

        <OptionsList
          options={currentQuestion.options}
          selectedOption={selectedAnswer !== null ? selectedAnswer : undefined}
          onOptionSelect={handleAnswerSelect}
          showAnswer={showAnswer}
          disabled={showAnswer}
        />

        {showAnswer && selectedAnswer !== null && (
          <AnswerFeedback
            isCorrect={isAnswerCorrect || false}
            explanation={currentQuestion.explanation}
            correctAnswer={['A', 'B', 'C', 'D'][currentQuestion.options.findIndex(opt => opt.isCorrect)] as 'A' | 'B' | 'C' | 'D'}
            selectedAnswer={['A', 'B', 'C', 'D'][selectedAnswer] as 'A' | 'B' | 'C' | 'D'}
          />
        )}
      </ScrollView>

      <View style={styles.actionContainer}>
        <View style={styles.navigationButtons}>
          <Button
            title="上一題"
            onPress={handlePrevious}
            variant="outline"
            disabled={!canGoPrevious}
            style={!canGoPrevious ? [styles.navButton, styles.disabledButton] as any : styles.navButton}
          />
          
          {!showAnswer ? (
            <Button
              title="提交答案"
              onPress={handleSubmitAnswer}
              disabled={selectedAnswer === null}
              style={selectedAnswer === null ? [styles.submitButton, styles.disabledButton] as any : styles.submitButton}
            />
          ) : (
            <Button
              title={isLastQuestion ? "完成練習" : "下一題"}
              onPress={handleNext}
              style={styles.submitButton}
            />
          )}
        </View>

        <Button
          title="結束練習"
          onPress={handleEndSession}
          variant="outline"
          style={styles.endButton}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  progressContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  actionContainer: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  navigationButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  navButton: {
    flex: 1,
  },
  submitButton: {
    flex: 2,
  },
  endButton: {
    width: '100%',
  },
  disabledButton: {
    opacity: 0.5,
  },
});