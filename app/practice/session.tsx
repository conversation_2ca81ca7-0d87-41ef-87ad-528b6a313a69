import { router } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import React, { useEffect } from 'react';
import { <PERSON><PERSON>, ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { Button } from '../../src/components/common';
import {
    AnswerFeedback,
    OptionsList,
    QuestionCard
} from '../../src/components/question';
import { useRealtimeAnswer } from '../../src/hooks/useRealtimeAnswer';
import { usePracticeStore } from '../../src/store';
import { COLORS } from '../../src/utils/constants';

export default function PracticeSessionScreen() {
  const {
    session,
    currentQuestion,
    selectedAnswer,
    showAnswer,
    isAnswerCorrect,
    stats,
    isActive,
    selectAnswer,
    submitAnswer,
    nextQuestion,
    previousQuestion,
    endSession,
  } = usePracticeStore();

  const { recordAnswer } = useRealtimeAnswer();

  useEffect(() => {
    if (!session || !isActive) {
      router.replace('/practice');
    }
  }, [session, isActive]);

  if (!session || !currentQuestion) {
    return null;
  }

  const handleAnswerSelect = (answerIndex: number) => {
    if (showAnswer) return;
    selectAnswer(answerIndex);
  };

  const handleSubmitAnswer = async () => {
    if (selectedAnswer === null || showAnswer) return;

    submitAnswer();

    // Record answer in database
    try {
      const correctOptionIndex = currentQuestion.options.findIndex(opt => opt.isCorrect);
      const selectedOptionLabel = ['A', 'B', 'C', 'D'][selectedAnswer];
      const correctOptionLabel = ['A', 'B', 'C', 'D'][correctOptionIndex];
      
      await recordAnswer({
        questionId: currentQuestion.id,
        volume: currentQuestion.volume,
        chapter: 1, // Default chapter
        isCorrect: currentQuestion.options[selectedAnswer]?.isCorrect || false,
        mode: 'practice',
        sessionId: session.id,
        selectedOption: selectedOptionLabel as 'A' | 'B' | 'C' | 'D',
        correctOption: correctOptionLabel as 'A' | 'B' | 'C' | 'D',
        timeSpent: 0, // TODO: Calculate actual time spent
      });
    } catch (error) {
      console.error('Failed to record answer:', error);
    }
  };

  const handleNext = () => {
    if (session.currentIndex < session.questions.length - 1) {
      nextQuestion();
    } else {
      // Reached end of current question set - offer to continue or finish
      Alert.alert(
        '已完成當前題目',
        `您已完成當前所有題目！\n\n正確率：${stats.accuracy.toFixed(1)}%\n答對：${stats.correctCount} 題\n答錯：${stats.wrongCount} 題\n\n您可以繼續練習更多題目或結束本次練習。`,
        [
          {
            text: '繼續練習',
            onPress: () => {
              // Reset to beginning for continuous practice
              router.replace('/practice');
            },
          },
          {
            text: '結束練習',
            onPress: () => {
              endSession();
              router.replace('/');
            },
          },
        ]
      );
    }
  };

  const handlePrevious = () => {
    if (session.currentIndex > 0) {
      previousQuestion();
    }
  };

  const handleEndSession = () => {
    Alert.alert(
      '結束練習',
      `本次練習統計：\n\n已完成：${stats.answeredCount} 題\n答對：${stats.correctCount} 題\n答錯：${stats.wrongCount} 題\n正確率：${stats.accuracy.toFixed(1)}%\n\n確定要結束練習嗎？`,
      [
        { text: '取消', style: 'cancel' },
        {
          text: '結束練習',
          onPress: () => {
            endSession();
            router.replace('/');
          },
        },
      ]
    );
  };

  const isLastQuestion = session.currentIndex === session.questions.length - 1;
  const canGoPrevious = session.currentIndex > 0;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      
      <View style={styles.progressContainer}>
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{stats.answeredCount}</Text>
            <Text style={styles.statLabel}>已完成</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: COLORS.SUCCESS }]}>{stats.correctCount}</Text>
            <Text style={styles.statLabel}>答對</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: COLORS.ERROR }]}>{stats.wrongCount}</Text>
            <Text style={styles.statLabel}>答錯</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statNumber, { color: COLORS.PRIMARY }]}>{stats.accuracy.toFixed(0)}%</Text>
            <Text style={styles.statLabel}>正確率</Text>
          </View>
        </View>
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <QuestionCard
          question={currentQuestion}
          questionNumber={session.currentIndex + 1}
        />

        <OptionsList
          options={currentQuestion.options}
          selectedOption={selectedAnswer !== null ? selectedAnswer : undefined}
          onOptionSelect={handleAnswerSelect}
          showAnswer={showAnswer}
          disabled={showAnswer}
        />

        {showAnswer && selectedAnswer !== null && (
          <AnswerFeedback
            isCorrect={isAnswerCorrect || false}
            explanation={currentQuestion.explanation}
            correctAnswer={['A', 'B', 'C', 'D'][currentQuestion.options.findIndex(opt => opt.isCorrect)] as 'A' | 'B' | 'C' | 'D'}
            selectedAnswer={['A', 'B', 'C', 'D'][selectedAnswer] as 'A' | 'B' | 'C' | 'D'}
          />
        )}
      </ScrollView>

      <View style={styles.actionContainer}>
        <View style={styles.navigationButtons}>
          <Button
            title="上一題"
            onPress={handlePrevious}
            variant="outline"
            disabled={!canGoPrevious}
            style={!canGoPrevious ? [styles.navButton, styles.disabledButton] as any : styles.navButton}
          />
          
          {!showAnswer ? (
            <Button
              title="提交答案"
              onPress={handleSubmitAnswer}
              disabled={selectedAnswer === null}
              style={selectedAnswer === null ? [styles.submitButton, styles.disabledButton] as any : styles.submitButton}
            />
          ) : (
            <Button
              title={isLastQuestion ? "繼續練習" : "下一題"}
              onPress={handleNext}
              style={styles.submitButton}
            />
          )}
        </View>

        <Button
          title="結束練習"
          onPress={handleEndSession}
          variant="outline"
          style={styles.endButton}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  progressContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: '600',
    color: COLORS.TEXT,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.SECONDARY_TEXT,
    marginTop: 4,
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  actionContainer: {
    padding: 16,
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
  },
  navigationButtons: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 12,
  },
  navButton: {
    flex: 1,
  },
  submitButton: {
    flex: 2,
  },
  endButton: {
    width: '100%',
  },
  disabledButton: {
    opacity: 0.5,
  },
});