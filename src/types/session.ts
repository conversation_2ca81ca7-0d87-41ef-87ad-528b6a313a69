import { ProcessedQuestion } from './question';

export interface PracticeSession {
  id: string;
  title: string;
  questions: ProcessedQuestion[];
  currentIndex: number;
  answers: Record<number, number>;
  startTime: Date;
  endTime?: Date;
  volumes: number[];
  config: PracticeConfig;
}

export interface PracticeConfig {
  volume: number | null;
  chapter: number | null;
  questionCount: number;
  mode: 'sequential' | 'random';
  includeWrongQuestions: boolean;
  includeBookmarked: boolean;
}

export interface SessionStats {
  totalQuestions: number;
  answeredCount: number;
  correctCount: number;
  wrongCount: number;
  accuracy: number;
  timeSpent: number;
}