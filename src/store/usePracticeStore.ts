import { create } from 'zustand';
import { ProcessedQuestion } from '../types/question';
import { PracticeSession, PracticeConfig, SessionStats } from '../types/session';

interface PracticeState {
  // Session data
  session: PracticeSession | null;
  isActive: boolean;
  isPaused: boolean;
  
  // Current question state
  currentQuestion: ProcessedQuestion | null;
  selectedAnswer: number | null;
  showAnswer: boolean;
  isAnswerCorrect: boolean | null;
  answerStartTime: Date | null;
  
  // Session stats
  stats: SessionStats;
  
  // UI state
  showExplanation: boolean;
  
  // Actions
  startSession: (config: PracticeConfig, questions: ProcessedQuestion[]) => void;
  setCurrentQuestion: (question: ProcessedQuestion) => void;
  selectAnswer: (answerIndex: number) => void;
  submitAnswer: () => void;
  nextQuestion: () => void;
  previousQuestion: () => void;
  pauseSession: () => void;
  resumeSession: () => void;
  endSession: () => void;
  toggleExplanation: () => void;
  reset: () => void;
}

const initialStats: SessionStats = {
  totalQuestions: 0,
  answeredCount: 0,
  correctCount: 0,
  wrongCount: 0,
  accuracy: 0,
  timeSpent: 0,
};

export const usePracticeStore = create<PracticeState>((set, get) => ({
  session: null,
  isActive: false,
  isPaused: false,
  currentQuestion: null,
  selectedAnswer: null,
  showAnswer: false,
  isAnswerCorrect: null,
  answerStartTime: null,
  stats: initialStats,
  showExplanation: false,

  startSession: (config, questions) => {
    const sessionId = `practice_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const session: PracticeSession = {
      id: sessionId,
      title: config.volume ? `第${config.volume}冊練習` : '綜合練習',
      questions,
      currentIndex: 0,
      answers: {},
      startTime: new Date(),
      volumes: config.volume ? [config.volume] : [1, 2, 3, 4, 5],
      config,
    };
    
    set({
      session,
      isActive: true,
      isPaused: false,
      currentQuestion: questions[0] || null,
      selectedAnswer: null,
      showAnswer: false,
      isAnswerCorrect: null,
      answerStartTime: new Date(),
      stats: {
        ...initialStats,
        totalQuestions: questions.length,
      },
    });
  },

  setCurrentQuestion: (question) => {
    set({
      currentQuestion: question,
      selectedAnswer: null,
      showAnswer: false,
      isAnswerCorrect: null,
      answerStartTime: new Date(),
    });
  },

  selectAnswer: (answerIndex) => {
    set({ selectedAnswer: answerIndex });
  },

  submitAnswer: () => {
    const { currentQuestion, selectedAnswer, session, answerStartTime, stats } = get();
    
    if (!currentQuestion || selectedAnswer === null || !session || !answerStartTime) {
      return;
    }

    const isCorrect = currentQuestion.options[selectedAnswer]?.isCorrect || false;
    const timeSpent = Date.now() - answerStartTime.getTime();
    
    // Update session answers
    const updatedAnswers = {
      ...session.answers,
      [session.currentIndex]: selectedAnswer,
    };

    const updatedStats: SessionStats = {
      ...stats,
      answeredCount: stats.answeredCount + 1,
      correctCount: stats.correctCount + (isCorrect ? 1 : 0),
      wrongCount: stats.wrongCount + (isCorrect ? 0 : 1),
      timeSpent: stats.timeSpent + timeSpent,
      accuracy: ((stats.correctCount + (isCorrect ? 1 : 0)) / (stats.answeredCount + 1)) * 100,
    };

    set({
      session: {
        ...session,
        answers: updatedAnswers,
      },
      isAnswerCorrect: isCorrect,
      showAnswer: true,
      stats: updatedStats,
    });
  },

  nextQuestion: () => {
    const { session } = get();
    if (!session || session.currentIndex >= session.questions.length - 1) {
      return;
    }

    const nextIndex = session.currentIndex + 1;
    const nextQuestion = session.questions[nextIndex];
    
    set({
      session: {
        ...session,
        currentIndex: nextIndex,
      },
      currentQuestion: nextQuestion,
      selectedAnswer: session.answers[nextIndex] || null,
      showAnswer: false,
      isAnswerCorrect: null,
      answerStartTime: new Date(),
      showExplanation: false,
    });
  },

  previousQuestion: () => {
    const { session } = get();
    if (!session || session.currentIndex <= 0) {
      return;
    }

    const prevIndex = session.currentIndex - 1;
    const prevQuestion = session.questions[prevIndex];
    const prevAnswer = session.answers[prevIndex];
    
    set({
      session: {
        ...session,
        currentIndex: prevIndex,
      },
      currentQuestion: prevQuestion,
      selectedAnswer: prevAnswer !== undefined ? prevAnswer : null,
      showAnswer: prevAnswer !== undefined,
      isAnswerCorrect: prevAnswer !== undefined ? prevQuestion.options[prevAnswer]?.isCorrect || false : null,
      answerStartTime: new Date(),
      showExplanation: false,
    });
  },

  pauseSession: () => {
    set({ isPaused: true });
  },

  resumeSession: () => {
    set({ 
      isPaused: false,
      answerStartTime: new Date(), // Reset timer
    });
  },

  endSession: () => {
    const { session } = get();
    if (session) {
      set({
        session: {
          ...session,
          endTime: new Date(),
        },
        isActive: false,
      });
    }
  },

  toggleExplanation: () => {
    set(state => ({ showExplanation: !state.showExplanation }));
  },

  reset: () => {
    set({
      session: null,
      isActive: false,
      isPaused: false,
      currentQuestion: null,
      selectedAnswer: null,
      showAnswer: false,
      isAnswerCorrect: null,
      answerStartTime: null,
      stats: initialStats,
      showExplanation: false,
    });
  },
}));