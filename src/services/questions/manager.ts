import shuffle from 'lodash.shuffle';
import { ProcessedQuestion } from '../../types/question';
import { PracticeConfig } from '../../types/session';
import { QuestionLoader } from './loader';

export class QuestionManager {
  static async generatePracticeQuestions(config: PracticeConfig): Promise<ProcessedQuestion[]> {
    try {
      let questions: ProcessedQuestion[] = [];

      // Load questions based on volume selection
      if (config.volume) {
        questions = await QuestionLoader.loadVolume(config.volume);
      } else {
        questions = await QuestionLoader.loadAllVolumes();
      }

      // TODO: Filter by chapter if needed in the future
      // Chapter filtering not implemented yet as questions don't have chapter property

      // TODO: Include wrong questions and bookmarked questions
      // This would require database queries to get user's wrong/bookmarked questions
      
      // Shuffle if random mode
      if (config.mode === 'random') {
        questions = shuffle(questions);
      }

      // Limit to requested count
      if (config.questionCount > 0 && config.questionCount < questions.length) {
        questions = questions.slice(0, config.questionCount);
      }

      return questions;
    } catch (error) {
      console.error('Failed to generate practice questions:', error);
      throw error;
    }
  }

  static async generateExamQuestions(): Promise<ProcessedQuestion[]> {
    try {
      const examQuestions: ProcessedQuestion[] = [];
      
      // Load questions from each volume according to exam rules
      const volumeDistribution = {
        1: 16, // 40%
        2: 8,  // 20%
        3: 8,  // 20%
        4: 8,  // 20% (combined from 4 and future 5)
      };

      for (const [volume, count] of Object.entries(volumeDistribution)) {
        const volumeNumber = parseInt(volume);
        const volumeQuestions = await QuestionLoader.loadVolume(volumeNumber);
        
        // Randomly select required number of questions from this volume
        const shuffledQuestions = shuffle(volumeQuestions);
        const selectedQuestions = shuffledQuestions.slice(0, count);
        
        examQuestions.push(...selectedQuestions);
      }

      // Final shuffle to mix all volumes
      return shuffle(examQuestions);
    } catch (error) {
      console.error('Failed to generate exam questions:', error);
      throw error;
    }
  }

  static validateQuestionSet(questions: ProcessedQuestion[]): boolean {
    if (questions.length === 0) {
      return false;
    }

    // Check each question has required fields
    return questions.every(q => 
      q.id &&
      q.volume &&
      q.question &&
      q.options &&
      Array.isArray(q.options) &&
      q.options.length === 4 &&
      q.options.some(opt => opt.isCorrect) // At least one correct answer
    );
  }

  static getQuestionsByVolume(questions: ProcessedQuestion[]): Record<number, ProcessedQuestion[]> {
    const grouped: Record<number, ProcessedQuestion[]> = {};
    
    questions.forEach(question => {
      if (!grouped[question.volume]) {
        grouped[question.volume] = [];
      }
      grouped[question.volume].push(question);
    });
    
    return grouped;
  }

  static getQuestionsByTags(questions: ProcessedQuestion[], volume?: number): Record<string, ProcessedQuestion[]> {
    let filteredQuestions = questions;
    
    if (volume) {
      filteredQuestions = questions.filter(q => q.volume === volume);
    }

    const grouped: Record<string, ProcessedQuestion[]> = {};
    
    filteredQuestions.forEach(question => {
      const tags = question.tags || ['未分類'];
      tags.forEach(tag => {
        const key = `${question.volume}-${tag}`;
        if (!grouped[key]) {
          grouped[key] = [];
        }
        grouped[key].push(question);
      });
    });
    
    return grouped;
  }
}